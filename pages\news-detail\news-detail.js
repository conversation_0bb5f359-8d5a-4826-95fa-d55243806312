// news-detail.js
Page({
  data: {
    article: {},
    contentSections: [],
    isLiked: false,
    isCollected: false,
    relatedArticles: [],
    comments: [],
    showCommentInput: false,
    commentText: ''
  },

  onLoad(options) {
    const articleId = options.id;
    if (articleId) {
      this.loadArticleDetail(articleId);
      this.loadRelatedArticles(articleId);
      this.loadComments(articleId);
    }
  },

  // 加载文章详情
  loadArticleDetail(articleId) {
    wx.showLoading({
      title: '加载中...'
    });

    // 模拟从服务器获取文章详情
    wx.request({
      url: 'https://jsonplaceholder.typicode.com/posts/' + articleId,
      method: 'GET',
      success: (res) => {
        console.log('文章详情请求成功:', res.data);
        // 使用模拟数据
        this.generateArticleDetail(articleId);
      },
      fail: (err) => {
        console.error('文章详情请求失败:', err);
        // 使用模拟数据
        this.generateArticleDetail(articleId);
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 生成文章详情数据
  generateArticleDetail(articleId) {
    const categories = ['exercise', 'diet', 'health', 'lifestyle'];
    const titles = [
      '每天30分钟运动，让你更健康',
      '科学饮食搭配，营养均衡很重要',
      '充足睡眠对健康的重要性',
      '如何养成良好的生活习惯',
      '瑜伽练习的正确方法',
      '健康减肥的科学方式',
      '维生素的作用与补充',
      '心理健康同样重要',
      '户外运动的好处',
      '健康饮水的重要性'
    ];

    const contents = [
      '运动是保持身体健康的重要方式。每天进行30分钟的中等强度运动，可以显著改善心血管健康，增强肌肉力量，提高免疫力。\n\n建议选择自己喜欢的运动方式，如快走、游泳、骑自行车等。运动不仅能够帮助控制体重，还能改善心情，减少压力。\n\n对于初学者，可以从低强度运动开始，逐渐增加运动量。记住，坚持比强度更重要，养成规律运动的习惯才能获得长期的健康益处。',
      '均衡的饮食是健康生活的基础。我们应该合理搭配蛋白质、碳水化合物、脂肪、维生素和矿物质。\n\n多吃新鲜蔬菜水果，它们富含维生素、矿物质和膳食纤维。适量摄入优质蛋白质，如鱼类、瘦肉、豆类等。选择复合碳水化合物，如全谷物、燕麦等。\n\n同时要控制糖分和盐分的摄入，避免过度加工的食品。每餐七分饱，细嚼慢咽，有助于消化和控制体重。',
      '充足的睡眠对身心健康至关重要。成年人每天需要7-9小时的优质睡眠。良好的睡眠有助于大脑恢复、记忆巩固、免疫系统修复。\n\n建议保持规律的作息时间，即使在周末也要尽量保持一致。创造良好的睡眠环境：保持房间安静、黑暗、凉爽。\n\n睡前避免使用电子设备，可以进行轻松的活动如阅读、冥想或听轻音乐。如果有睡眠问题，应及时咨询医生。',
      '良好的生活习惯是健康长寿的秘诀。包括规律作息、适量运动、均衡饮食、戒烟限酒、保持良好心态等。\n\n这些习惯需要长期坚持，从小事做起。比如每天定时起床、饭后散步、多喝水、保持房间整洁等。\n\n养成习惯需要时间和耐心，建议一次只培养一个新习惯，等它变成自然行为后再添加新的。记住，小的改变积累起来会产生巨大的影响。',
      '瑜伽是一种古老而有效的身心锻炼方式。正确的瑜伽练习可以提高柔韧性、增强核心力量、改善平衡能力、缓解压力。\n\n初学者应该从基础动作开始，如山式、下犬式、婴儿式等。注意呼吸配合，动作要缓慢平稳，避免过度拉伸。\n\n建议在专业教练指导下学习，或者跟随可靠的在线教程。每次练习20-30分钟即可，重要的是保持规律性。瑜伽不仅锻炼身体，更是心灵的修炼。',
      '科学减肥需要合理控制饮食和增加运动。不建议极端节食或使用不安全的减肥产品。健康的减肥速度是每周0.5-1公斤。\n\n制定可持续的饮食计划：减少高热量食物，增加蔬菜水果，控制份量但不要饿肚子。结合有氧运动和力量训练，提高基础代谢率。\n\n保持耐心和毅力，减肥是一个长期过程。关注整体健康而不仅仅是体重数字。建立健康的生活方式比快速减肥更重要。',
      '维生素是维持人体正常生理功能必需的营养素。不同的维生素有不同的作用，如维生素C增强免疫力，维生素D促进钙吸收，维生素B群支持神经系统。\n\n最好通过均衡饮食获得维生素：多吃新鲜蔬果获得维生素C，适当晒太阳合成维生素D，摄入全谷物获得B族维生素。\n\n必要时可在医生指导下补充维生素制剂，但不要盲目大量补充。过量摄入某些维生素也可能对健康有害。',
      '心理健康与身体健康同样重要。保持积极乐观的心态，学会管理压力，建立良好的人际关系，培养兴趣爱好，都有助于心理健康。\n\n学会识别和表达自己的情绪，不要压抑负面情绪。可以通过运动、音乐、艺术等方式释放压力。保持社交联系，与家人朋友分享感受。\n\n遇到心理问题时，要及时寻求专业帮助。心理咨询和治疗可以有效帮助解决心理困扰，提高生活质量。',
      '户外运动不仅能锻炼身体，还能亲近自然，改善心情。新鲜空气和阳光有助于维生素D的合成，提高免疫力。\n\n可以选择适合的户外活动：徒步、跑步、骑行、游泳等。户外运动还能增加社交机会，结识志同道合的朋友。\n\n注意防晒和安全防护，选择合适的装备。根据天气和个人体能选择运动强度。即使是简单的户外散步也比室内久坐要好得多。',
      '水是生命之源，正确饮水对健康至关重要。成年人每天应饮用1.5-2升水，具体需求因人而异。\n\n要选择清洁的饮用水，白开水是最好的选择。避免过量饮用含糖饮料、咖啡因饮料。可以适量喝茶，但不要用茶代替白水。\n\n运动时要及时补充水分，少量多次饮用。注意观察尿液颜色，淡黄色表示水分充足。保持身体水分平衡对新陈代谢和排毒都很重要。'
    ];

    const authors = ['张医生', '李营养师', '王教练', '赵专家', '陈老师'];
    const categoryNames = ['运动健身', '饮食营养', '健康知识', '生活方式'];

    const index = (parseInt(articleId) - 1) % titles.length;
    const category = categories[index % categories.length];
    const categoryName = categoryNames[index % categoryNames.length];

    const article = {
      id: articleId,
      title: titles[index],
      content: contents[index],
      category: categoryName,
      categoryId: category,
      image: `https://via.placeholder.com/750x400/4CAF50/FFFFFF?text=${categoryName}`,
      author: authors[index % authors.length],
      publishTime: this.formatPublishTime(new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)),
      views: Math.floor(Math.random() * 1000) + 100,
      likes: Math.floor(Math.random() * 50) + 10,
      tags: this.generateTags(category)
    };

    // 将内容按段落分割
    const contentSections = article.content.split('\n\n').filter(section => section.trim());

    this.setData({
      article: article,
      contentSections: contentSections
    });

    // 增加阅读量
    this.increaseViews(articleId);
  },

  // 格式化发布时间
  formatPublishTime(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 生成标签
  generateTags(category) {
    const tagMap = {
      'exercise': ['运动健身', '体能训练', '健康生活'],
      'diet': ['营养饮食', '健康食谱', '营养搭配'],
      'health': ['健康知识', '医学科普', '养生保健'],
      'lifestyle': ['生活方式', '健康习惯', '生活品质']
    };
    return tagMap[category] || ['健康生活'];
  },

  // 增加阅读量
  increaseViews(articleId) {
    // 模拟增加阅读量的API调用
    wx.request({
      url: 'https://jsonplaceholder.typicode.com/posts/' + articleId,
      method: 'PUT',
      data: {
        views: this.data.article.views + 1
      },
      success: (res) => {
        console.log('阅读量更新成功');
      },
      fail: (err) => {
        console.error('阅读量更新失败:', err);
      }
    });
  },

  // 加载相关文章
  loadRelatedArticles(currentId) {
    // 模拟相关文章数据
    const relatedArticles = [];
    for (let i = 1; i <= 3; i++) {
      const id = (parseInt(currentId) + i) % 10 + 1;
      relatedArticles.push({
        id: id,
        title: `相关文章标题 ${id}`,
        category: '健康知识',
        time: '2天前',
        image: `https://via.placeholder.com/150x100/4CAF50/FFFFFF?text=文章${id}`
      });
    }

    this.setData({
      relatedArticles: relatedArticles
    });
  },

  // 加载评论
  loadComments(articleId) {
    const mockComments = [
      {
        id: 1,
        author: '健康达人',
        avatar: 'https://via.placeholder.com/60x60/4CAF50/FFFFFF?text=用户',
        content: '这篇文章写得很好，很实用！',
        time: '2小时前'
      },
      {
        id: 2,
        author: '运动爱好者',
        avatar: 'https://via.placeholder.com/60x60/2196F3/FFFFFF?text=用户',
        content: '学到了很多知识，感谢分享。',
        time: '5小时前'
      }
    ];

    this.setData({
      comments: mockComments
    });
  },

  // 切换点赞状态
  toggleLike() {
    const isLiked = !this.data.isLiked;
    this.setData({ isLiked: isLiked });

    wx.showToast({
      title: isLiked ? '点赞成功' : '取消点赞',
      icon: 'success'
    });

    // 模拟点赞API调用
    wx.request({
      url: 'https://jsonplaceholder.typicode.com/posts',
      method: 'POST',
      data: {
        articleId: this.data.article.id,
        action: isLiked ? 'like' : 'unlike'
      },
      success: (res) => {
        console.log('点赞操作成功');
      }
    });
  },

  // 切换收藏状态
  toggleCollect() {
    const isCollected = !this.data.isCollected;
    this.setData({ isCollected: isCollected });

    wx.showToast({
      title: isCollected ? '收藏成功' : '取消收藏',
      icon: 'success'
    });
  },

  // 显示评论输入框
  showCommentInput() {
    this.setData({ showCommentInput: true });
  },

  // 隐藏评论输入框
  hideCommentInput() {
    this.setData({
      showCommentInput: false,
      commentText: ''
    });
  },

  // 评论输入
  onCommentInput(e) {
    this.setData({
      commentText: e.detail.value
    });
  },

  // 提交评论
  submitComment() {
    const commentText = this.data.commentText.trim();
    if (!commentText) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      });
      return;
    }

    const newComment = {
      id: Date.now(),
      author: '我',
      avatar: 'https://via.placeholder.com/60x60/FF9800/FFFFFF?text=我',
      content: commentText,
      time: '刚刚'
    };

    this.setData({
      comments: [newComment, ...this.data.comments],
      showCommentInput: false,
      commentText: ''
    });

    wx.showToast({
      title: '评论发布成功',
      icon: 'success'
    });

    // 模拟提交评论到服务器
    wx.request({
      url: 'https://jsonplaceholder.typicode.com/comments',
      method: 'POST',
      data: {
        articleId: this.data.article.id,
        content: commentText
      },
      success: (res) => {
        console.log('评论提交成功');
      }
    });
  },

  // 查看相关文章
  viewRelatedArticle(e) {
    const articleId = e.currentTarget.dataset.id;
    wx.redirectTo({
      url: `/pages/news-detail/news-detail?id=${articleId}`
    });
  },

  // 分享文章
  onShareAppMessage() {
    return {
      title: this.data.article.title,
      path: `/pages/news-detail/news-detail?id=${this.data.article.id}`,
      imageUrl: this.data.article.image
    };
  }
})
