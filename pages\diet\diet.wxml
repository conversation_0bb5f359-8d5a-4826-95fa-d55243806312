<!--diet.wxml-->
<view class="container">
  <!-- 今日饮食摄入 -->
  <view class="intake-card">
    <view class="intake-title">今日营养摄入</view>
    <view class="intake-progress">
      <view class="progress-item">
        <view class="progress-label">卡路里</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{caloriePercent}}%"></view>
        </view>
        <view class="progress-text">{{todayIntake.calories}}/{{dailyTarget.calories}}</view>
      </view>
      <view class="progress-item">
        <view class="progress-label">蛋白质</view>
        <view class="progress-bar">
          <view class="progress-fill protein" style="width: {{proteinPercent}}%"></view>
        </view>
        <view class="progress-text">{{todayIntake.protein}}g/{{dailyTarget.protein}}g</view>
      </view>
      <view class="progress-item">
        <view class="progress-label">碳水化合物</view>
        <view class="progress-bar">
          <view class="progress-fill carbs" style="width: {{carbsPercent}}%"></view>
        </view>
        <view class="progress-text">{{todayIntake.carbs}}g/{{dailyTarget.carbs}}g</view>
      </view>
    </view>
  </view>

  <!-- 今日用餐记录 -->
  <view class="section">
    <view class="section-title">
      <text>今日用餐</text>
      <button class="add-btn" bindtap="addMeal">记录用餐</button>
    </view>
    <view class="meal-list">
      <view class="meal-time" wx:for="{{mealTimes}}" wx:key="time">
        <view class="time-header">
          <text class="time-name">{{item.name}}</text>
          <text class="time-calories">{{item.totalCalories}}卡路里</text>
        </view>
        <view class="food-list" wx:if="{{item.foods.length > 0}}">
          <view class="food-item" wx:for="{{item.foods}}" wx:key="id" wx:for-item="food">
            <image src="{{food.image}}" class="food-image"></image>
            <view class="food-info">
              <view class="food-name">{{food.name}}</view>
              <view class="food-detail">{{food.amount}} · {{food.calories}}卡路里</view>
            </view>
            <view class="food-action" bindtap="deleteMeal" data-time="{{item.time}}" data-id="{{food.id}}">
              <icon type="clear" size="16" color="#999"/>
            </view>
          </view>
        </view>
        <view class="empty-meal" wx:else bindtap="addMealToTime" data-time="{{item.time}}">
          <text class="empty-text">点击添加{{item.name}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 健康食谱推荐 -->
  <view class="section">
    <view class="section-title">健康食谱推荐</view>
    <view class="recipe-grid">
      <view class="recipe-item" wx:for="{{recipes}}" wx:key="id" bindtap="viewRecipe" data-id="{{item.id}}">
        <image src="{{item.image}}" class="recipe-image"></image>
        <view class="recipe-info">
          <view class="recipe-name">{{item.name}}</view>
          <view class="recipe-meta">
            <text class="recipe-time">{{item.cookTime}}分钟</text>
            <text class="recipe-calories">{{item.calories}}卡路里</text>
          </view>
          <view class="recipe-tags">
            <text class="recipe-tag" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 营养知识 -->
  <view class="section">
    <view class="section-title">营养小贴士</view>
    <view class="tips-list">
      <view class="tip-item" wx:for="{{nutritionTips}}" wx:key="id">
        <view class="tip-icon">💡</view>
        <view class="tip-content">
          <view class="tip-title">{{item.title}}</view>
          <view class="tip-description">{{item.description}}</view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 添加用餐弹窗 -->
<view class="meal-modal" wx:if="{{showMealModal}}">
  <view class="modal-mask" bindtap="cancelAddMeal"></view>
  <view class="modal-dialog">
    <view class="modal-header">
      <text class="modal-title">记录用餐</text>
      <view class="modal-close" bindtap="cancelAddMeal">×</view>
    </view>
    <view class="modal-content">
      <view class="form-item">
        <text class="form-label">用餐时间：</text>
        <picker mode="selector" range="{{mealTimeOptions}}" bindchange="onMealTimeChange">
          <view class="picker">{{selectedMealTime || '请选择用餐时间'}}</view>
        </picker>
      </view>
      <view class="form-item">
        <text class="form-label">食物名称：</text>
        <input class="form-input" placeholder="请输入食物名称" bindinput="onFoodNameInput" value="{{newMeal.name}}"/>
      </view>
      <view class="form-item">
        <text class="form-label">食用量：</text>
        <input class="form-input" placeholder="如：1碗、100g" bindinput="onFoodAmountInput" value="{{newMeal.amount}}"/>
      </view>
      <view class="form-item">
        <text class="form-label">卡路里：</text>
        <input class="form-input" type="number" placeholder="卡路里" bindinput="onFoodCaloriesInput" value="{{newMeal.calories}}"/>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="cancelAddMeal">取消</button>
      <button class="modal-btn confirm" bindtap="confirmAddMeal">添加</button>
    </view>
  </view>
</view>
