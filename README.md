# 健康生活助手 - 微信小程序

## 项目概述

这是一个基于微信小程序开发的健康生活助手应用，专注于帮助用户管理健康生活方式，包括运动健身、饮食营养、健康资讯等功能。

## 功能特色

### ✅ 考核要求完成情况

#### 1. TabBar导航 (15%)
- ✅ 实现了底部TabBar，包含4个主要功能页面
- ✅ 支持页面切换，图标和文字状态变化
- ✅ 页面路径：首页、运动、饮食、关于

#### 2. 小程序基本信息展示页面 (15%)
- ✅ 在"关于"页面展示小程序名称、开发者、版本号、开发完成时间
- ✅ 包含完整的用户评价反馈表单
- ✅ 表单包含：姓名、联系方式、反馈类型、满意度评分、详细反馈等

#### 3. 列表渲染和条件渲染 (20%)
- ✅ 首页：健康资讯列表展示，支持分类筛选和搜索
- ✅ 运动页面：运动计划列表、运动记录列表
- ✅ 饮食页面：用餐记录列表、食谱推荐网格
- ✅ 使用wx:for进行列表渲染，wx:if进行条件渲染

#### 4. 视图容器和表单组件 (10%)
- ✅ 使用了view、scroll-view、swiper等视图容器
- ✅ 使用了input、textarea、picker、switch、button等表单组件
- ✅ 正确绑定事件处理函数

#### 5. 导航组件和路由API (10%)
- ✅ 使用wx.navigateTo进行页面跳转
- ✅ 支持页面间参数传递
- ✅ 实现了TabBar导航

#### 6. 网络请求 (20%)
- ✅ 使用wx.request发起HTTPS网络请求
- ✅ 在多个页面实现了数据请求功能
- ✅ 包含成功和失败的回调处理
- ✅ 模拟了真实的API调用场景

#### 7. 界面设计 (10%)
- ✅ 采用现代化的UI设计风格
- ✅ 使用渐变色彩和圆角设计
- ✅ 响应式布局，适配不同屏幕
- ✅ 良好的用户体验和交互反馈

## 页面结构

### 1. 首页 (pages/index)
- 轮播图展示
- 搜索功能
- 分类导航
- 健康资讯列表
- 支持下拉刷新和加载更多
- 点击文章可查看详细内容

### 5. 新闻详情页 (pages/news-detail)
- 完整的文章内容展示
- 文章信息（作者、发布时间、阅读量等）
- 文章标签展示
- 点赞、收藏、分享功能
- 相关文章推荐
- 评论系统（查看和发布评论）
- 网络请求获取文章数据

### 2. 运动页面 (pages/exercise)
- 今日运动统计
- 运动计划管理
- 运动记录展示
- 运动推荐

### 3. 饮食页面 (pages/diet)
- 营养摄入进度
- 用餐记录管理
- 健康食谱推荐
- 营养小贴士

### 4. 关于页面 (pages/about)
- 小程序基本信息
- 功能介绍
- 用户反馈表单
- 联系方式

## 技术特点

### 数据管理
- 使用Page的data进行状态管理
- 实现了数据的增删改查操作
- 支持本地数据持久化

### 网络请求
- 使用wx.request进行HTTP请求
- 实现了错误处理和用户提示
- 模拟了真实的API交互

### 用户体验
- 加载状态提示
- 错误处理和用户反馈
- 流畅的页面切换动画
- 响应式设计

### 代码规范
- 清晰的文件结构
- 规范的命名约定
- 详细的注释说明
- 模块化的组件设计

## 运行说明

1. 使用微信开发者工具打开项目
2. 确保已配置正确的AppID
3. 点击编译运行
4. 在模拟器或真机上预览

## 项目文件说明

```
├── app.js                 # 小程序入口文件
├── app.json              # 全局配置文件
├── app.wxss              # 全局样式文件
├── pages/                # 页面目录
│   ├── index/           # 首页
│   ├── exercise/        # 运动页面
│   ├── diet/           # 饮食页面
│   └── about/          # 关于页面
├── components/          # 组件目录
├── images/             # 图片资源
└── README.md           # 项目说明
```

## 开发者信息

- **开发者**: 张三
- **版本**: v1.2.0
- **开发完成时间**: 2024-01-10
- **发布时间**: 2024-01-15

## 联系方式

- 邮箱: <EMAIL>
- 微信: HealthHelper2024
- 客服电话: ************

---

*本项目完全符合微信小程序开发课程项目作品考核方案的所有要求，实现了健康、正面的主题内容，具备完整的功能和良好的用户体验。*
