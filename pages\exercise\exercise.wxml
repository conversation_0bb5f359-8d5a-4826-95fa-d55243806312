<!--exercise.wxml-->
<view class="container">
  <!-- 今日运动统计 -->
  <view class="stats-card">
    <view class="stats-title">今日运动统计</view>
    <view class="stats-content">
      <view class="stat-item">
        <view class="stat-number">{{todayStats.steps}}</view>
        <view class="stat-label">步数</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{todayStats.calories}}</view>
        <view class="stat-label">卡路里</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{todayStats.duration}}</view>
        <view class="stat-label">运动时长(分钟)</view>
      </view>
    </view>
  </view>

  <!-- 运动计划 -->
  <view class="section">
    <view class="section-title">
      <text>运动计划</text>
      <button class="add-btn" bindtap="addPlan">添加计划</button>
    </view>
    <view class="plan-list">
      <view class="plan-item {{item.completed ? 'completed' : ''}}" 
            wx:for="{{exercisePlans}}" wx:key="id"
            bindtap="togglePlan" data-id="{{item.id}}">
        <view class="plan-info">
          <view class="plan-name">{{item.name}}</view>
          <view class="plan-detail">{{item.duration}}分钟 · {{item.calories}}卡路里</view>
        </view>
        <view class="plan-status">
          <icon type="{{item.completed ? 'success' : 'circle'}}" size="20" color="{{item.completed ? '#4CAF50' : '#ccc'}}"/>
        </view>
      </view>
    </view>
  </view>

  <!-- 运动记录 -->
  <view class="section">
    <view class="section-title">
      <text>运动记录</text>
      <button class="record-btn" bindtap="startRecord">开始记录</button>
    </view>
    <view class="record-list">
      <view class="record-item" wx:for="{{exerciseRecords}}" wx:key="id">
        <view class="record-icon">
          <image src="{{item.icon}}" class="exercise-icon"></image>
        </view>
        <view class="record-info">
          <view class="record-name">{{item.name}}</view>
          <view class="record-time">{{item.date}} {{item.time}}</view>
        </view>
        <view class="record-data">
          <view class="record-duration">{{item.duration}}分钟</view>
          <view class="record-calories">{{item.calories}}卡路里</view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 添加计划弹窗 -->
<modal title="添加运动计划" hidden="{{!showPlanModal}}" confirm-text="添加" cancel-text="取消" bindconfirm="confirmAddPlan" bindcancel="cancelAddPlan">
  <view class="modal-content">
    <view class="form-item">
      <text class="form-label">运动名称：</text>
      <input class="form-input" placeholder="请输入运动名称" bindinput="onPlanNameInput" value="{{newPlan.name}}"/>
    </view>
    <view class="form-item">
      <text class="form-label">运动时长：</text>
      <input class="form-input" type="number" placeholder="分钟" bindinput="onPlanDurationInput" value="{{newPlan.duration}}"/>
    </view>
  </view>
</modal>
