<!--about.wxml-->
<view class="container">
  <!-- 小程序信息卡片 -->
  <view class="app-info-card">
    <view class="app-logo">
      <image src="/images/logo.png" class="logo-image" mode="aspectFit"></image>
    </view>
    <view class="app-details">
      <view class="app-name">{{appInfo.name}}</view>
      <view class="app-description">{{appInfo.description}}</view>
      <view class="app-meta">
        <view class="meta-item">
          <text class="meta-label">开发者：</text>
          <text class="meta-value">{{appInfo.developer}}</text>
        </view>
        <view class="meta-item">
          <text class="meta-label">版本号：</text>
          <text class="meta-value">{{appInfo.version}}</text>
        </view>
        <view class="meta-item">
          <text class="meta-label">发布时间：</text>
          <text class="meta-value">{{appInfo.releaseDate}}</text>
        </view>
        <view class="meta-item">
          <text class="meta-label">开发完成时间：</text>
          <text class="meta-value">{{appInfo.developmentDate}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能介绍 -->
  <view class="section">
    <view class="section-title">功能介绍</view>
    <view class="feature-list">
      <view class="feature-item" wx:for="{{features}}" wx:key="id">
        <view class="feature-icon">{{item.icon}}</view>
        <view class="feature-content">
          <view class="feature-name">{{item.name}}</view>
          <view class="feature-description">{{item.description}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 用户反馈表单 -->
  <view class="section">
    <view class="section-title">用户反馈</view>
    <form bindsubmit="submitFeedback" bindreset="resetFeedback">
      <view class="feedback-form">
        <view class="form-group">
          <text class="form-label">您的姓名</text>
          <input class="form-input" name="userName" placeholder="请输入您的姓名" value="{{feedback.userName}}" bindinput="onUserNameInput"/>
        </view>
        
        <view class="form-group">
          <text class="form-label">联系方式</text>
          <input class="form-input" name="contact" placeholder="请输入手机号或邮箱" value="{{feedback.contact}}" bindinput="onContactInput"/>
        </view>
        
        <view class="form-group">
          <text class="form-label">反馈类型</text>
          <picker mode="selector" range="{{feedbackTypes}}" name="feedbackType" bindchange="onFeedbackTypeChange">
            <view class="picker-view">{{selectedFeedbackType || '请选择反馈类型'}}</view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">满意度评分</text>
          <view class="rating-container">
            <view class="star {{index < rating ? 'active' : ''}}" 
                  wx:for="{{5}}" wx:key="*this" 
                  bindtap="onRatingChange" data-rating="{{index + 1}}">
              ★
            </view>
            <text class="rating-text">{{ratingTexts[rating - 1] || '请评分'}}</text>
          </view>
        </view>
        
        <view class="form-group">
          <text class="form-label">详细反馈</text>
          <textarea class="form-textarea" name="content" placeholder="请详细描述您的意见或建议..." 
                    value="{{feedback.content}}" bindinput="onContentInput" maxlength="500"></textarea>
          <view class="char-count">{{feedback.content.length}}/500</view>
        </view>
        
        <view class="form-group">
          <text class="form-label">是否愿意接受回访</text>
          <switch name="allowCallback" checked="{{feedback.allowCallback}}" bindchange="onCallbackChange"/>
        </view>
        
        <view class="form-actions">
          <button class="submit-btn" form-type="submit">提交反馈</button>
          <button class="reset-btn" form-type="reset">重置</button>
        </view>
      </view>
    </form>
  </view>

  <!-- 联系我们 -->
  <view class="section">
    <view class="section-title">联系我们</view>
    <view class="contact-list">
      <view class="contact-item" bindtap="copyText" data-text="{{contact.email}}">
        <view class="contact-icon">📧</view>
        <view class="contact-content">
          <view class="contact-label">邮箱</view>
          <view class="contact-value">{{contact.email}}</view>
        </view>
        <view class="contact-action">点击复制</view>
      </view>
      
      <view class="contact-item" bindtap="copyText" data-text="{{contact.wechat}}">
        <view class="contact-icon">💬</view>
        <view class="contact-content">
          <view class="contact-label">微信</view>
          <view class="contact-value">{{contact.wechat}}</view>
        </view>
        <view class="contact-action">点击复制</view>
      </view>
      
      <view class="contact-item" bindtap="makePhoneCall">
        <view class="contact-icon">📞</view>
        <view class="contact-content">
          <view class="contact-label">客服电话</view>
          <view class="contact-value">{{contact.phone}}</view>
        </view>
        <view class="contact-action">点击拨打</view>
      </view>
    </view>
  </view>

  <!-- 版权信息 -->
  <view class="copyright">
    <text class="copyright-text">© 2024 健康生活助手. All rights reserved.</text>
    <text class="copyright-text">让健康生活更简单</text>
  </view>
</view>
