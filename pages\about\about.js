// about.js
Page({
  data: {
    // 小程序基本信息
    appInfo: {
      name: "健康生活助手",
      description: "专注于健康生活方式的综合性小程序，提供运动健身、饮食营养、健康资讯等功能。",
      developer: "张三",
      version: "v1.2.0",
      releaseDate: "2024-01-15",
      developmentDate: "2024-01-10"
    },

    // 功能特色
    features: [
      {
        id: 1,
        icon: "🏃‍♂️",
        name: "运动健身",
        description: "记录运动计划，追踪运动数据，提供专业运动指导"
      },
      {
        id: 2,
        icon: "🥗",
        name: "饮食营养",
        description: "营养摄入分析，健康食谱推荐，饮食习惯管理"
      },
      {
        id: 3,
        icon: "📰",
        name: "健康资讯",
        description: "最新健康知识，专业医学科普，生活健康贴士"
      },
      {
        id: 4,
        icon: "📊",
        name: "数据统计",
        description: "健康数据可视化，趋势分析，个性化建议"
      }
    ],

    // 反馈表单数据
    feedback: {
      userName: '',
      contact: '',
      content: '',
      allowCallback: false
    },

    // 反馈类型选项
    feedbackTypes: ['功能建议', '问题反馈', '使用体验', '内容建议', '其他'],
    selectedFeedbackType: '',
    selectedFeedbackTypeIndex: -1,

    // 评分相关
    rating: 0,
    ratingTexts: ['很不满意', '不满意', '一般', '满意', '非常满意'],

    // 联系方式
    contact: {
      email: "<EMAIL>",
      wechat: "HealthHelper2024",
      phone: "************"
    }
  },

  onLoad() {
    this.loadAppInfo();
  },

  // 加载应用信息
  loadAppInfo() {
    // 模拟从服务器获取应用信息
    wx.request({
      url: 'https://jsonplaceholder.typicode.com/posts/1',
      method: 'GET',
      success: (res) => {
        console.log('应用信息加载成功:', res.data);
        // 这里可以处理从服务器获取的应用信息
      },
      fail: (err) => {
        console.error('应用信息加载失败:', err);
      }
    });
  },

  // 用户名输入
  onUserNameInput(e) {
    this.setData({
      'feedback.userName': e.detail.value
    });
  },

  // 联系方式输入
  onContactInput(e) {
    this.setData({
      'feedback.contact': e.detail.value
    });
  },

  // 反馈类型选择
  onFeedbackTypeChange(e) {
    const index = e.detail.value;
    this.setData({
      selectedFeedbackType: this.data.feedbackTypes[index],
      selectedFeedbackTypeIndex: index
    });
  },

  // 评分变化
  onRatingChange(e) {
    const rating = e.currentTarget.dataset.rating;
    this.setData({ rating: rating });
  },

  // 反馈内容输入
  onContentInput(e) {
    this.setData({
      'feedback.content': e.detail.value
    });
  },

  // 回访开关
  onCallbackChange(e) {
    this.setData({
      'feedback.allowCallback': e.detail.value
    });
  },

  // 提交反馈
  submitFeedback(e) {
    const formData = e.detail.value;
    const { rating, selectedFeedbackType } = this.data;

    // 表单验证
    if (!formData.userName.trim()) {
      wx.showToast({
        title: '请输入您的姓名',
        icon: 'none'
      });
      return;
    }

    if (!formData.contact.trim()) {
      wx.showToast({
        title: '请输入联系方式',
        icon: 'none'
      });
      return;
    }

    if (!selectedFeedbackType) {
      wx.showToast({
        title: '请选择反馈类型',
        icon: 'none'
      });
      return;
    }

    if (rating === 0) {
      wx.showToast({
        title: '请进行满意度评分',
        icon: 'none'
      });
      return;
    }

    if (!formData.content.trim()) {
      wx.showToast({
        title: '请输入详细反馈',
        icon: 'none'
      });
      return;
    }

    // 构建提交数据
    const submitData = {
      ...formData,
      feedbackType: selectedFeedbackType,
      rating: rating,
      ratingText: this.data.ratingTexts[rating - 1],
      submitTime: new Date().toISOString()
    };

    // 发送反馈到服务器
    wx.request({
      url: 'https://jsonplaceholder.typicode.com/posts',
      method: 'POST',
      data: submitData,
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('反馈提交成功:', res.data);
        wx.showModal({
          title: '提交成功',
          content: '感谢您的反馈！我们会认真处理您的意见和建议。',
          showCancel: false,
          success: () => {
            this.resetFeedback();
          }
        });
      },
      fail: (err) => {
        console.error('反馈提交失败:', err);
        wx.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 重置反馈表单
  resetFeedback() {
    this.setData({
      feedback: {
        userName: '',
        contact: '',
        content: '',
        allowCallback: false
      },
      selectedFeedbackType: '',
      selectedFeedbackTypeIndex: -1,
      rating: 0
    });
  },

  // 复制文本
  copyText(e) {
    const text = e.currentTarget.dataset.text;
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  // 拨打电话
  makePhoneCall() {
    wx.makePhoneCall({
      phoneNumber: this.data.contact.phone,
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (err) => {
        console.error('拨打电话失败:', err);
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        });
      }
    });
  },

  // 分享小程序
  onShareAppMessage() {
    return {
      title: '健康生活助手 - 让健康生活更简单',
      path: '/pages/index/index',
      imageUrl: '/images/share.png'
    };
  },

  // 页面显示时刷新数据
  onShow() {
    // 可以在这里刷新页面数据
  }
})
