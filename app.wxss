/**app.wxss**/
/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: #333;
}

/* 通用容器 */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 通用按钮样式 */
.btn {
  border-radius: 50rpx;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border: none;
  margin: 0;
}

.btn-primary {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.btn-secondary {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #ddd;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  margin-bottom: 20rpx;
}

/* 文本样式 */
.text-primary {
  color: #4CAF50;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

/* 间距工具类 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-center {
  text-align: center;
}
