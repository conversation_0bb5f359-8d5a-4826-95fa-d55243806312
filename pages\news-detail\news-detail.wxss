/**news-detail.wxss**/
page {
  background-color: #f5f5f5;
}

.container {
  padding-bottom: 40rpx;
}

/* 文章头部 */
.article-header {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.article-image {
  width: 100%;
  height: 100%;
}

.article-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.7));
  padding: 60rpx 30rpx 30rpx;
  color: white;
}

.article-category {
  background: rgba(255,255,255,0.2);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  display: inline-block;
  margin-bottom: 15rpx;
}

.article-title {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1.4;
}

/* 文章信息 */
.article-info {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-info {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
}

.author-details {
  flex: 1;
}

.author-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.publish-time {
  font-size: 24rpx;
  color: #666;
}

.article-stats {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-text {
  font-size: 24rpx;
  color: #666;
}

/* 文章标签 */
.article-tags {
  background: white;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
}

.tag {
  display: inline-block;
  background: #f0f0f0;
  color: #4CAF50;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  margin-right: 15rpx;
  margin-bottom: 10rpx;
}

/* 文章内容 */
.article-content {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.content-section {
  margin-bottom: 30rpx;
}

.content-section:last-child {
  margin-bottom: 0;
}

.content-text {
  font-size: 30rpx;
  line-height: 1.8;
  color: #333;
  text-align: justify;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  background: white;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 50rpx;
  background: white;
  color: #666;
  font-size: 26rpx;
  margin: 0;
}

.action-btn.liked {
  border-color: #ff6b6b;
  color: #ff6b6b;
}

.action-btn.collected {
  border-color: #4CAF50;
  color: #4CAF50;
}

/* 相关推荐 */
.related-section {
  background: white;
  margin-bottom: 20rpx;
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-bottom: 1rpx solid #f0f0f0;
  color: #333;
}

.related-list {
  padding: 20rpx 30rpx;
}

.related-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.related-item:last-child {
  border-bottom: none;
}

.related-image {
  width: 150rpx;
  height: 100rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.related-content {
  flex: 1;
}

.related-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.related-meta {
  display: flex;
  gap: 15rpx;
  font-size: 22rpx;
  color: #999;
}

.related-category {
  background: #4CAF50;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}

/* 评论区 */
.comment-section {
  background: white;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-comment-btn {
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  margin: 0;
}

/* 评论输入 */
.comment-input-area {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.comment-input {
  width: 100%;
  min-height: 120rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background: #fafafa;
  box-sizing: border-box;
  margin-bottom: 15rpx;
}

.comment-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.char-count {
  font-size: 22rpx;
  color: #999;
}

.submit-comment-btn {
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  margin: 0 0 0 10rpx;
}

.cancel-comment-btn {
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #ddd;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  margin: 0;
}

/* 评论列表 */
.comment-list {
  padding: 20rpx 30rpx;
}

.comment-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.comment-author {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.comment-time {
  font-size: 22rpx;
  color: #999;
}

.comment-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 空评论状态 */
.empty-comments {
  text-align: center;
  padding: 60rpx 30rpx;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
}
