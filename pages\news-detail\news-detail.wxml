<!--news-detail.wxml-->
<view class="container">
  <!-- 文章头部 -->
  <view class="article-header">
    <image src="{{article.image}}" class="article-image" mode="aspectFill"></image>
    <view class="article-overlay">
      <view class="article-category">{{article.category}}</view>
      <view class="article-title">{{article.title}}</view>
    </view>
  </view>

  <!-- 文章信息 -->
  <view class="article-info">
    <view class="info-row">
      <view class="author-info">
        <image src="/images/avatar.png" class="author-avatar"></image>
        <view class="author-details">
          <view class="author-name">{{article.author}}</view>
          <view class="publish-time">{{article.publishTime}}</view>
        </view>
      </view>
      <view class="article-stats">
        <view class="stat-item">
          <icon type="{{isLiked ? 'success' : 'circle'}}" size="16" color="{{isLiked ? '#ff6b6b' : '#999'}}"/>
          <text class="stat-text">{{article.likes + (isLiked ? 1 : 0)}}</text>
        </view>
        <view class="stat-item">
          <icon type="info" size="16" color="#999"/>
          <text class="stat-text">{{article.views}}次阅读</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 文章标签 -->
  <view class="article-tags" wx:if="{{article.tags && article.tags.length > 0}}">
    <text class="tag" wx:for="{{article.tags}}" wx:key="*this"># {{item}}</text>
  </view>

  <!-- 文章内容 -->
  <view class="article-content">
    <view class="content-section" wx:for="{{contentSections}}" wx:key="index">
      <text class="content-text">{{item}}</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="action-btn like-btn {{isLiked ? 'liked' : ''}}" bindtap="toggleLike">
      <icon type="{{isLiked ? 'success' : 'circle'}}" size="20" color="{{isLiked ? '#ff6b6b' : '#666'}}"/>
      <text>{{isLiked ? '已点赞' : '点赞'}}</text>
    </button>
    <button class="action-btn share-btn" open-type="share">
      <icon type="info_circle" size="20" color="#666"/>
      <text>分享</text>
    </button>
    <button class="action-btn collect-btn {{isCollected ? 'collected' : ''}}" bindtap="toggleCollect">
      <icon type="{{isCollected ? 'success' : 'circle'}}" size="20" color="{{isCollected ? '#4CAF50' : '#666'}}"/>
      <text>{{isCollected ? '已收藏' : '收藏'}}</text>
    </button>
  </view>

  <!-- 相关推荐 -->
  <view class="related-section" wx:if="{{relatedArticles.length > 0}}">
    <view class="section-title">相关推荐</view>
    <view class="related-list">
      <view class="related-item" wx:for="{{relatedArticles}}" wx:key="id" bindtap="viewRelatedArticle" data-id="{{item.id}}">
        <image src="{{item.image}}" class="related-image"></image>
        <view class="related-content">
          <view class="related-title">{{item.title}}</view>
          <view class="related-meta">
            <text class="related-category">{{item.category}}</text>
            <text class="related-time">{{item.time}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 评论区 -->
  <view class="comment-section">
    <view class="section-title">
      <text>评论 ({{comments.length}})</text>
      <button class="add-comment-btn" bindtap="showCommentInput">写评论</button>
    </view>
    
    <!-- 评论输入框 -->
    <view class="comment-input-area" wx:if="{{showCommentInput}}">
      <textarea class="comment-input" placeholder="写下你的想法..." bindinput="onCommentInput" value="{{commentText}}" maxlength="200"></textarea>
      <view class="comment-actions">
        <text class="char-count">{{commentText.length}}/200</text>
        <button class="submit-comment-btn" bindtap="submitComment">发布</button>
        <button class="cancel-comment-btn" bindtap="hideCommentInput">取消</button>
      </view>
    </view>

    <!-- 评论列表 -->
    <view class="comment-list" wx:if="{{comments.length > 0}}">
      <view class="comment-item" wx:for="{{comments}}" wx:key="id">
        <image src="{{item.avatar}}" class="comment-avatar"></image>
        <view class="comment-content">
          <view class="comment-header">
            <text class="comment-author">{{item.author}}</text>
            <text class="comment-time">{{item.time}}</text>
          </view>
          <view class="comment-text">{{item.content}}</view>
        </view>
      </view>
    </view>

    <!-- 空评论状态 -->
    <view class="empty-comments" wx:else>
      <text class="empty-text">暂无评论，快来发表第一条评论吧！</text>
    </view>
  </view>
</view>
