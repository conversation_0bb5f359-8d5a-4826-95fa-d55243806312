// exercise.js
Page({
  data: {
    todayStats: {
      steps: 8520,
      calories: 320,
      duration: 45
    },
    exercisePlans: [
      {
        id: 1,
        name: "晨跑",
        duration: 30,
        calories: 200,
        completed: true
      },
      {
        id: 2,
        name: "瑜伽",
        duration: 45,
        calories: 150,
        completed: false
      },
      {
        id: 3,
        name: "力量训练",
        duration: 60,
        calories: 300,
        completed: false
      }
    ],
    exerciseRecords: [
      {
        id: 1,
        name: "跑步",
        date: "2024-01-15",
        time: "07:00",
        duration: 30,
        calories: 200,
        icon: "/images/run.png"
      },
      {
        id: 2,
        name: "游泳",
        date: "2024-01-14",
        time: "19:30",
        duration: 45,
        calories: 280,
        icon: "/images/swim.png"
      }
    ],
    showPlanModal: false,
    newPlan: {
      name: '',
      duration: ''
    }
  },

  onLoad() {
  },

  // 切换计划完成状态
  togglePlan(e) {
    const planId = e.currentTarget.dataset.id;
    const plans = this.data.exercisePlans.map(plan => {
      if (plan.id === planId) {
        return { ...plan, completed: !plan.completed };
      }
      return plan;
    });
    
    this.setData({ exercisePlans: plans });
    
    const plan = plans.find(p => p.id === planId);
    wx.showToast({
      title: plan.completed ? '计划已完成' : '计划已取消',
      icon: 'success'
    });
  },

  // 添加计划
  addPlan() {
    this.setData({ showPlanModal: true });
  },

  // 确认添加计划
  confirmAddPlan() {
    const { name, duration } = this.data.newPlan;
    
    if (!name || !duration) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }

    const newPlan = {
      id: Date.now(),
      name: name,
      duration: parseInt(duration),
      calories: parseInt(duration) * 5, // 简单计算卡路里
      completed: false
    };

    this.setData({
      exercisePlans: [...this.data.exercisePlans, newPlan],
      showPlanModal: false,
      newPlan: { name: '', duration: '' }
    });

    wx.showToast({
      title: '计划添加成功',
      icon: 'success'
    });
  },

  // 取消添加计划
  cancelAddPlan() {
    this.setData({
      showPlanModal: false,
      newPlan: { name: '', duration: '' }
    });
  },

  // 计划名称输入
  onPlanNameInput(e) {
    this.setData({
      'newPlan.name': e.detail.value
    });
  },

  // 计划时长输入
  onPlanDurationInput(e) {
    this.setData({
      'newPlan.duration': e.detail.value
    });
  },

  // 开始记录运动
  startRecord() {
    wx.navigateTo({
      url: '/pages/exercise-record/exercise-record'
    });
  },

  // 查看运动推荐
  viewRecommendation(e) {
    const recommendId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/exercise-detail/exercise-detail?id=${recommendId}`
    });
  }
})
