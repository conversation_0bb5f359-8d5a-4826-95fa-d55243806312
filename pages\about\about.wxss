/**about.wxss**/
page {
  background-color: #f5f5f5;
}

.container {
  padding: 20rpx;
  padding-bottom: 40rpx;
}

/* 应用信息卡片 */
.app-info-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  color: white;
  text-align: center;
}

.app-logo {
  margin-bottom: 30rpx;
}

.logo-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: white;
}

.app-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.app-description {
  font-size: 26rpx;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 30rpx;
}

.app-meta {
  text-align: left;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 0;
  font-size: 24rpx;
  border-bottom: 1rpx solid rgba(255,255,255,0.2);
}

.meta-item:last-child {
  border-bottom: none;
}

.meta-label {
  opacity: 0.8;
}

.meta-value {
  font-weight: bold;
}

/* 区块样式 */
.section {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-bottom: 1rpx solid #f0f0f0;
  color: #333;
}

/* 功能列表 */
.feature-list {
  padding: 20rpx 30rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  font-size: 50rpx;
  margin-right: 25rpx;
}

.feature-content {
  flex: 1;
}

.feature-name {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #333;
}

.feature-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 反馈表单 */
.feedback-form {
  padding: 30rpx;
}

.form-group {
  margin-bottom: 35rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input {
  width: 100%;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 25rpx;
  font-size: 28rpx;
  background: #fafafa;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
  background: white;
}

.picker-view {
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 25rpx;
  font-size: 28rpx;
  background: #fafafa;
  color: #333;
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 25rpx;
  font-size: 28rpx;
  background: #fafafa;
  box-sizing: border-box;
  line-height: 1.6;
}

.char-count {
  text-align: right;
  font-size: 22rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 评分组件 */
.rating-container {
  display: flex;
  align-items: center;
}

.star {
  font-size: 50rpx;
  color: #ddd;
  margin-right: 10rpx;
  transition: color 0.2s;
}

.star.active {
  color: #FFD700;
}

.rating-text {
  margin-left: 20rpx;
  font-size: 26rpx;
  color: #666;
}

/* 表单按钮 */
.form-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.submit-btn {
  flex: 2;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 30rpx;
  font-weight: bold;
  margin: 0;
}

.reset-btn {
  flex: 1;
  background: #f5f5f5;
  color: #666;
  border: 1rpx solid #ddd;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 28rpx;
  margin: 0;
}

/* 联系方式 */
.contact-list {
  padding: 20rpx 30rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-icon {
  font-size: 40rpx;
  margin-right: 25rpx;
}

.contact-content {
  flex: 1;
}

.contact-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.contact-value {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.contact-action {
  font-size: 24rpx;
  color: #667eea;
  padding: 10rpx 20rpx;
  border: 1rpx solid #667eea;
  border-radius: 20rpx;
}

/* 版权信息 */
.copyright {
  text-align: center;
  padding: 40rpx 20rpx;
  color: #999;
}

.copyright-text {
  display: block;
  font-size: 24rpx;
  line-height: 1.8;
}
