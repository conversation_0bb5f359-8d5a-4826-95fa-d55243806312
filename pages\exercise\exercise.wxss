/**exercise.wxss**/
page {
  background-color: #f5f5f5;
}

.container {
  padding: 20rpx;
}

/* 统计卡片 */
.stats-card {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  color: white;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
}

.stats-content {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 区块样式 */
.section {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-bottom: 1rpx solid #f0f0f0;
}

.add-btn, .record-btn {
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  margin: 0;
}

/* 计划列表 */
.plan-list {
  padding: 0 30rpx 20rpx;
}

.plan-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: opacity 0.3s;
}

.plan-item.completed {
  opacity: 0.6;
}

.plan-item:last-child {
  border-bottom: none;
}

.plan-info {
  flex: 1;
}

.plan-name {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.plan-detail {
  font-size: 26rpx;
  color: #666;
}

.plan-status {
  margin-left: 20rpx;
}

/* 记录列表 */
.record-list {
  padding: 0 30rpx 20rpx;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-icon {
  margin-right: 20rpx;
}

.exercise-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
}

.record-info {
  flex: 1;
}

.record-name {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.record-time {
  font-size: 24rpx;
  color: #666;
}

.record-data {
  text-align: right;
}

.record-duration {
  font-size: 28rpx;
  font-weight: bold;
  color: #4CAF50;
}

.record-calories {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

/* 推荐滚动 */
.recommend-scroll {
  padding: 20rpx 30rpx;
  white-space: nowrap;
}

.recommend-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
  vertical-align: top;
}

.recommend-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 15rpx;
  margin-bottom: 10rpx;
}

.recommend-info {
  text-align: center;
}

.recommend-name {
  font-size: 26rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.recommend-level {
  font-size: 22rpx;
  color: #666;
}

/* 弹窗样式 */
.modal-content {
  padding: 20rpx;
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.form-label {
  width: 150rpx;
  font-size: 28rpx;
}

.form-input {
  flex: 1;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 15rpx;
  font-size: 28rpx;
}
