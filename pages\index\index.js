// index.js
Page({
  data: {
    banners: [
      {
        id: 1,
        title: "健康生活从今天开始",
        image: "/images/banner1.png"
      },
      {
        id: 2,
        title: "科学运动，强健体魄",
        image: "/images/banner2.png"
      },
      {
        id: 3,
        title: "均衡饮食，营养健康",
        image: "/images/banner3.png"
      }
    ],
    categories: [
      { id: 'all', name: '全部' },
      { id: 'exercise', name: '运动健身' },
      { id: 'diet', name: '饮食营养' },
      { id: 'health', name: '健康知识' },
      { id: 'lifestyle', name: '生活方式' }
    ],
    currentCategory: 'all',
    searchKeyword: '',
    newsList: [],
    filteredNews: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10
  },

  onLoad() {
    this.loadNewsData();
  },

  // 加载新闻数据
  loadNewsData() {
    this.setData({ loading: true });

    // 模拟网络请求
    setTimeout(() => {
      const mockData = this.generateMockNews();
      const newsList = this.data.page === 1 ? mockData : [...this.data.newsList, ...mockData];

      this.setData({
        newsList: newsList,
        filteredNews: this.filterNews(newsList),
        loading: false,
        hasMore: mockData.length === this.data.pageSize
      });
    }, 1000);
  },

  // 生成模拟新闻数据
  generateMockNews() {
    const categories = ['exercise', 'diet', 'health', 'lifestyle'];
    const titles = [
      '每天30分钟运动，让你更健康',
      '科学饮食搭配，营养均衡很重要',
      '充足睡眠对健康的重要性',
      '如何养成良好的生活习惯',
      '瑜伽练习的正确方法',
      '健康减肥的科学方式',
      '维生素的作用与补充',
      '心理健康同样重要',
      '户外运动的好处',
      '健康饮水的重要性'
    ];

    const mockNews = [];
    const startIndex = (this.data.page - 1) * this.data.pageSize;

    for (let i = 0; i < this.data.pageSize && startIndex + i < 50; i++) {
      const index = startIndex + i;
      const category = categories[index % categories.length];
      const categoryName = this.data.categories.find(c => c.id === category)?.name || '健康知识';

      mockNews.push({
        id: index + 1,
        title: titles[index % titles.length] + ` (${index + 1})`,
        summary: '这是一篇关于健康生活的文章摘要，内容丰富实用，值得一读。',
        category: categoryName,
        categoryId: category,
        image: `/images/cover.png`,
        time: this.formatTime(new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)),
        views: Math.floor(Math.random() * 1000) + 100
      });
    }

    return mockNews;
  },

  // 格式化时间
  formatTime(date) {
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (24 * 60 * 60 * 1000));

    if (days === 0) {
      return '今天';
    } else if (days === 1) {
      return '昨天';
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return `${date.getMonth() + 1}-${date.getDate()}`;
    }
  },

  // 过滤新闻
  filterNews(newsList) {
    let filtered = newsList;

    // 按分类过滤
    if (this.data.currentCategory !== 'all') {
      filtered = filtered.filter(news => news.categoryId === this.data.currentCategory);
    }

    // 按关键词过滤
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filtered = filtered.filter(news =>
        news.title.toLowerCase().includes(keyword) ||
        news.summary.toLowerCase().includes(keyword)
      );
    }

    return filtered;
  },

  // 分类切换
  onCategoryChange(e) {
    const categoryId = e.currentTarget.dataset.id;
    this.setData({
      currentCategory: categoryId,
      filteredNews: this.filterNews(this.data.newsList)
    });
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value,
      filteredNews: this.filterNews(this.data.newsList)
    });
  },

  // 搜索按钮
  onSearch() {
    this.setData({
      filteredNews: this.filterNews(this.data.newsList)
    });
    wx.showToast({
      title: `搜索"${this.data.searchKeyword}"`,
      icon: 'none'
    });
  },

  // 新闻详情
  onNewsDetail(e) {
    const newsId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/news-detail/news-detail?id=${newsId}`
    });
  },

  // 加载更多
  loadMore() {
    if (this.data.loading || !this.data.hasMore) return;

    this.setData({
      page: this.data.page + 1
    });
    this.loadNewsData();
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({
      page: 1,
      newsList: [],
      hasMore: true
    });
    this.loadNewsData();
    wx.stopPullDownRefresh();
  }
})
