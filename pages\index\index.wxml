<!--index.wxml-->
<view class="container">
  <!-- 轮播图 -->
  <swiper class="banner" indicator-dots="true" autoplay="true" interval="3000" duration="500">
    <swiper-item wx:for="{{banners}}" wx:key="id">
      <image src="{{item.image}}" class="banner-image" mode="aspectFill"></image>
      <view class="banner-text">{{item.title}}</view>
    </swiper-item>
  </swiper>

  <!-- 搜索框 -->
  <view class="search-container">
    <input class="search-input" placeholder="搜索健康资讯..." bindinput="onSearchInput" value="{{searchKeyword}}"/>
    <button class="search-btn" bindtap="onSearch">搜索</button>
  </view>

  <!-- 分类导航 -->
  <view class="category-nav">
    <view class="category-item {{currentCategory === item.id ? 'active' : ''}}"
          wx:for="{{categories}}" wx:key="id"
          bindtap="onCategoryChange" data-id="{{item.id}}">
      {{item.name}}
    </view>
  </view>

  <!-- 健康资讯列表 -->
  <view class="news-list">
    <view class="news-item" wx:for="{{filteredNews}}" wx:key="id" bindtap="onNewsDetail" data-id="{{item.id}}">
      <image src="{{item.image}}" class="news-image" mode="aspectFill"></image>
      <view class="news-content">
        <view class="news-title">{{item.title}}</view>
        <view class="news-summary">{{item.summary}}</view>
        <view class="news-meta">
          <text class="news-category">{{item.category}}</text>
          <text class="news-time">{{item.time}}</text>
          <text class="news-views">{{item.views}}次阅读</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}" bindtap="loadMore">
    <text wx:if="{{!loading}}">点击加载更多</text>
    <text wx:else>加载中...</text>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!hasMore && filteredNews.length > 0}}">
    没有更多数据了
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredNews.length === 0 && !loading}}">
    <image src="/images/empty.png" class="empty-image"></image>
    <text class="empty-text">暂无相关资讯</text>
  </view>
</view>
