/**diet.wxss**/
page {
  background-color: #f5f5f5;
}

.container {
  padding: 20rpx;
}

/* 营养摄入卡片 */
.intake-card {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  color: white;
}

.intake-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-align: center;
}

.intake-progress {
  space-y: 20rpx;
}

.progress-item {
  margin-bottom: 25rpx;
}

.progress-label {
  font-size: 26rpx;
  margin-bottom: 10rpx;
  opacity: 0.9;
}

.progress-bar {
  height: 12rpx;
  background: rgba(255,255,255,0.3);
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.progress-fill {
  height: 100%;
  background: white;
  border-radius: 6rpx;
  transition: width 0.3s;
}

.progress-fill.protein {
  background: #4CAF50;
}

.progress-fill.carbs {
  background: #2196F3;
}

.progress-text {
  font-size: 22rpx;
  text-align: right;
  opacity: 0.9;
}

/* 区块样式 */
.section {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  border-bottom: 1rpx solid #f0f0f0;
}

.add-btn {
  background: #FF9800;
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  margin: 0;
}

/* 用餐列表 */
.meal-list {
  padding: 0 30rpx 20rpx;
}

.meal-time {
  margin-bottom: 30rpx;
}

.time-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.time-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.time-calories {
  font-size: 24rpx;
  color: #FF9800;
  font-weight: bold;
}

.food-list {
  padding-top: 15rpx;
}

.food-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.food-item:last-child {
  border-bottom: none;
}

.food-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.food-info {
  flex: 1;
}

.food-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.food-detail {
  font-size: 24rpx;
  color: #666;
}

.food-action {
  padding: 10rpx;
}

.empty-meal {
  text-align: center;
  padding: 40rpx 20rpx;
  color: #999;
  border: 2rpx dashed #ddd;
  border-radius: 10rpx;
  margin-top: 15rpx;
}

.empty-text {
  font-size: 26rpx;
}

/* 食谱网格 */
.recipe-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 30rpx;
  gap: 20rpx;
}

.recipe-item {
  width: calc(50% - 10rpx);
  background: #f8f8f8;
  border-radius: 15rpx;
  overflow: hidden;
}

.recipe-image {
  width: 100%;
  height: 200rpx;
}

.recipe-info {
  padding: 20rpx;
}

.recipe-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.recipe-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.recipe-tag {
  background: #FF9800;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
}

/* 营养小贴士 */
.tips-list {
  padding: 20rpx 30rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.tip-item:last-child {
  border-bottom: none;
}

.tip-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  margin-top: 5rpx;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.tip-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 自定义弹窗样式 */
.meal-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
}

.modal-dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.modal-content {
  padding: 30rpx;
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.form-label {
  width: 150rpx;
  font-size: 28rpx;
  color: #333;
}

.form-input {
  flex: 1;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

.picker {
  flex: 1;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  border: none;
  padding: 30rpx;
  font-size: 28rpx;
  margin: 0;
  border-radius: 0;
}

.modal-btn.cancel {
  background: #f5f5f5;
  color: #666;
}

.modal-btn.confirm {
  background: #FF9800;
  color: white;
}
