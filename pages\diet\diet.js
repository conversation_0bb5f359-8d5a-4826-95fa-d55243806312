// diet.js
Page({
  data: {
    // 今日营养摄入
    todayIntake: {
      calories: 1200,
      protein: 45,
      carbs: 150
    },
    // 每日目标
    dailyTarget: {
      calories: 2000,
      protein: 60,
      carbs: 250
    },
    // 用餐时间
    mealTimes: [
      {
        time: 'breakfast',
        name: '早餐',
        totalCalories: 350,
        foods: [
          {
            id: 1,
            name: '燕麦粥',
            amount: '1碗',
            calories: 200,
            image: '/images/粥.png'
          },
          {
            id: 2,
            name: '鸡蛋',
            amount: '1个',
            calories: 150,
            image: '/images/鸡蛋.png'
          }
        ]
      },
      {
        time: 'lunch',
        name: '午餐',
        totalCalories: 550,
        foods: [
          {
            id: 3,
            name: '鸡胸肉沙拉',
            amount: '1份',
            calories: 350,
            image: '/images/沙拉.png'
          },
          {
            id: 4,
            name: '苹果',
            amount: '1个',
            calories: 200,
            image: '/images/苹果.png'
          }
        ]
      },
      {
        time: 'dinner',
        name: '晚餐',
        totalCalories: 300,
        foods: [
          {
            id: 5,
            name: '蒸蛋羹',
            amount: '1碗',
            calories: 300,
            image: '/images/蛋羹.png'
          }
        ]
      },
      {
        time: 'snack',
        name: '加餐',
        totalCalories: 0,
        foods: []
      }
    ],
    // 食谱推荐
    recipes: [
      {
        id: 1,
        name: '蔬菜沙拉',
        cookTime: 10,
        calories: 150,
        image: '/images/蔬菜沙拉.png',
        tags: ['低卡', '素食', '快手']
      },
      {
        id: 2,
        name: '鸡胸肉炒饭',
        cookTime: 20,
        calories: 400,
        image: '/images/鸡胸肉炒饭.png',
        tags: ['高蛋白', '主食']
      },
      {
        id: 3,
        name: '蒸蛋羹',
        cookTime: 15,
        calories: 200,
        image: '/images/蒸蛋羹.png',
        tags: ['嫩滑', '营养']
      },
      {
        id: 4,
        name: '水果拼盘',
        cookTime: 5,
        calories: 100,
        image: '/images/水果拼盘.png',
        tags: ['维生素', '清爽']
      }
    ],
    // 营养小贴士
    nutritionTips: [
      {
        id: 1,
        title: '多喝水',
        description: '每天至少喝8杯水，有助于新陈代谢和排毒。'
      },
      {
        id: 2,
        title: '均衡饮食',
        description: '合理搭配蛋白质、碳水化合物和脂肪的比例。'
      },
      {
        id: 3,
        title: '少食多餐',
        description: '一天可以分4-5次进食，有助于控制血糖。'
      }
    ],
    // 弹窗相关
    showMealModal: false,
    mealTimeOptions: ['早餐', '午餐', '晚餐', '加餐'],
    selectedMealTime: '',
    selectedMealTimeKey: '',
    newMeal: {
      name: '',
      amount: '',
      calories: ''
    }
  },

  onLoad() {
    this.calculateProgress();
  },

  // 计算营养摄入进度
  calculateProgress() {
    const { todayIntake, dailyTarget } = this.data;
    this.setData({
      caloriePercent: Math.min((todayIntake.calories / dailyTarget.calories) * 100, 100),
      proteinPercent: Math.min((todayIntake.protein / dailyTarget.protein) * 100, 100),
      carbsPercent: Math.min((todayIntake.carbs / dailyTarget.carbs) * 100, 100)
    });
  },

  // 添加用餐记录
  addMeal() {
    this.setData({ showMealModal: true });
  },

  // 添加到指定时间的用餐
  addMealToTime(e) {
    const mealTime = e.currentTarget.dataset.time;
    const timeMap = {
      'breakfast': '早餐',
      'lunch': '午餐', 
      'dinner': '晚餐',
      'snack': '加餐'
    };
    
    this.setData({
      showMealModal: true,
      selectedMealTime: timeMap[mealTime],
      selectedMealTimeKey: mealTime
    });
  },

  // 用餐时间选择
  onMealTimeChange(e) {
    const index = e.detail.value;
    const timeKeys = ['breakfast', 'lunch', 'dinner', 'snack'];
    this.setData({
      selectedMealTime: this.data.mealTimeOptions[index],
      selectedMealTimeKey: timeKeys[index]
    });
  },

  // 食物名称输入
  onFoodNameInput(e) {
    this.setData({
      'newMeal.name': e.detail.value
    });
  },

  // 食用量输入
  onFoodAmountInput(e) {
    this.setData({
      'newMeal.amount': e.detail.value
    });
  },

  // 卡路里输入
  onFoodCaloriesInput(e) {
    this.setData({
      'newMeal.calories': e.detail.value
    });
  },

  // 确认添加用餐
  confirmAddMeal() {
    const { selectedMealTimeKey, newMeal } = this.data;
    
    if (!selectedMealTimeKey || !newMeal.name || !newMeal.amount || !newMeal.calories) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }

    const newFood = {
      id: Date.now(),
      name: newMeal.name,
      amount: newMeal.amount,
      calories: parseInt(newMeal.calories),
      image: `https://via.placeholder.com/80x80/4CAF50/FFFFFF?text=${newMeal.name.charAt(0)}`
    };

    const mealTimes = this.data.mealTimes.map(mealTime => {
      if (mealTime.time === selectedMealTimeKey) {
        return {
          ...mealTime,
          foods: [...mealTime.foods, newFood],
          totalCalories: mealTime.totalCalories + newFood.calories
        };
      }
      return mealTime;
    });

    // 更新今日摄入
    const newTodayIntake = {
      ...this.data.todayIntake,
      calories: this.data.todayIntake.calories + newFood.calories
    };

    this.setData({
      mealTimes: mealTimes,
      todayIntake: newTodayIntake,
      showMealModal: false,
      selectedMealTime: '',
      selectedMealTimeKey: '',
      newMeal: { name: '', amount: '', calories: '' }
    });

    this.calculateProgress();

    wx.showToast({
      title: '添加成功',
      icon: 'success'
    });
  },

  // 取消添加用餐
  cancelAddMeal() {
    this.setData({
      showMealModal: false,
      selectedMealTime: '',
      selectedMealTimeKey: '',
      newMeal: { name: '', amount: '', calories: '' }
    });
  },

  // 删除用餐记录
  deleteMeal(e) {
    const { time, id } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条用餐记录吗？',
      success: (res) => {
        if (res.confirm) {
          const mealTimes = this.data.mealTimes.map(mealTime => {
            if (mealTime.time === time) {
              const foodToDelete = mealTime.foods.find(food => food.id == id);
              const newFoods = mealTime.foods.filter(food => food.id != id);
              return {
                ...mealTime,
                foods: newFoods,
                totalCalories: mealTime.totalCalories - (foodToDelete ? foodToDelete.calories : 0)
              };
            }
            return mealTime;
          });

          this.setData({ mealTimes: mealTimes });
          this.calculateProgress();

          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    });
  },

  // 查看食谱详情
  viewRecipe(e) {
    const recipeId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/recipe-detail/recipe-detail?id=${recipeId}`
    });
  }
})
