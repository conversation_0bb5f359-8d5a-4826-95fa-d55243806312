/**index.wxss**/
page {
  background-color: #f5f5f5;
}

.container {
  padding-bottom: 20rpx;
}

/* 轮播图样式 */
.banner {
  height: 300rpx;
  position: relative;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-text {
  position: absolute;
  bottom: 20rpx;
  left: 30rpx;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
}

/* 搜索框样式 */
.search-container {
  display: flex;
  padding: 20rpx 30rpx;
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 50rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.search-input {
  flex: 1;
  padding: 10rpx 20rpx;
  font-size: 28rpx;
}

.search-btn {
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 10rpx 30rpx;
  font-size: 28rpx;
  margin: 0;
}

/* 分类导航样式 */
.category-nav {
  display: flex;
  padding: 20rpx 30rpx;
  background: white;
  margin: 0 30rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.category-item {
  flex: 1;
  text-align: center;
  padding: 15rpx 10rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 15rpx;
  transition: all 0.3s;
}

.category-item.active {
  background: #4CAF50;
  color: white;
}

/* 新闻列表样式 */
.news-list {
  padding: 0 30rpx;
}

.news-item {
  display: flex;
  background: white;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.news-item:active {
  transform: scale(0.98);
}

.news-image {
  width: 200rpx;
  height: 150rpx;
  flex-shrink: 0;
}

.news-content {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.news-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-summary {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-meta {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #999;
}

.news-category {
  background: #4CAF50;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  margin-right: 15rpx;
}

.news-time {
  margin-right: 15rpx;
}

.news-views {
  margin-left: auto;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 30rpx;
  color: #4CAF50;
  font-size: 28rpx;
}

.no-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 26rpx;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 100rpx 30rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}
